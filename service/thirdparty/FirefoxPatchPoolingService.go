package thirdparty

import (
	"crypto/tls"
	"encoding/json"
	"github.com/PuerkitoBio/goquery"
	"io"
	"net/http"
	_ "net/url"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"strconv"
	"strings"
	"time"
)

var fireFoxMetaData = map[string]interface{}{
	"uuid": "4a437872-ea6e-4fc5-89d3-78d53d1af38a",
	"templateFileNameMap": map[string]interface{}{
		"x64": "FIREFOX_X64.xml",
		"x86": "FIREFOX_X32.xml",
	},
}

var (
	Host             = "https://ftp.mozilla.org"
	ReleaseUrlPath   = "/pub/firefox/releases/"
	PackageUrlPath   = "/pub/firefox/releases/${version}/${platform}/${language_code}/"
	CheckSumUrlPath  = "/pub/firefox/releases/${version}/SHA256SUMS"
	LatestVersionApi = "https://product-details.mozilla.org/1.0/firefox_versions.json"
)

type FirefoxPatchPoolingService struct {
	ThirdPartyPackageService
}

func (c FirefoxPatchPoolingService) Name() string {
	return "FirefoxPatchPoolingService"
}
func init() {
	RegisterCollector(FirefoxPatchPoolingService{})
}

func NewFirefoxPatchPoolingService() *FirefoxPatchPoolingService {
	return &FirefoxPatchPoolingService{}
}

func (service FirefoxPatchPoolingService) ExecuteSync() {

	thirdPartyRepo := NewThirdPartyPackageService().Repository
	latestVersion := ""

	htmlContent, err := service.parseUrlWebPageToHtmlcontent(LatestVersionApi)
	if err == nil {
		var fireFoxVersionDetails map[string]interface{}
		err := json.Unmarshal([]byte(htmlContent), &fireFoxVersionDetails)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}

		if fireFoxVersionDetails != nil && len(fireFoxVersionDetails) >= 0 {
			if version, ok := fireFoxVersionDetails["LATEST_FIREFOX_VERSION"]; ok {
				latestVersion = version.(string)
			}
		}
	}

	// Fallback for latest version details
	if latestVersion == "" {
		url := Host + ReleaseUrlPath
		htmlContent, err = service.parseUrlWebPageToHtmlcontent(url)
		if err != nil {
			logger.ServiceLogger.Error("Error while parsing Firefox release details :", err.Error())
			return
		}

		versionList := service.parseVersionListFromReleasePage(err, htmlContent)

		for _, version := range versionList {
			if common.CompareVersions(version, latestVersion) > 0 {
				latestVersion = version
			}
		}
	}

	count, err := thirdPartyRepo.GetPkgCountByLatestVersion(latestVersion, int(common.FIREFOX))
	if count == 0 {
		checkSumMap := map[string]string{}
		checksumUrl := Host + CheckSumUrlPath
		checksumUrl = strings.Replace(checksumUrl, "${version}", latestVersion, 1)
		checksumHtmlContent, err := service.parseUrlWebPageToHtmlcontent(checksumUrl)
		if err != nil {
			logger.ServiceLogger.Error("Error while parsing checksum details :", err.Error())
		}
		checksumDetailsList := strings.Split(checksumHtmlContent, "\n")
		if len(checksumDetailsList) > 0 {
			for _, checksumDetails := range checksumDetailsList {
				checksum := strings.SplitN(checksumDetails, "  ", 2)
				if len(checksum) == 2 {
					key := strings.Trim(checksum[1], " ")
					value := strings.Trim(checksum[0], " ")
					checkSumMap[key] = value
				} else {
					logger.ServiceLogger.Info("Firefox checksum details:", checksumDetails)
				}
			}
		}

		//supportedOs := []string{"mac", "win64", "linux-x86_64", "linux-i686"}
		supportedOs := []string{"win64"}
		supportedLanguageCode := []string{"en-US"}
		for _, os := range supportedOs {
			for _, languageCode := range supportedLanguageCode {
				pkgUrl := Host + PackageUrlPath
				pkgUrl = strings.Replace(pkgUrl, "${version}", latestVersion, 1)
				pkgUrl = strings.Replace(pkgUrl, "${platform}", os, 1)
				pkgUrl = strings.Replace(pkgUrl, "${language_code}", languageCode, 1)

				htmlContent, err = service.parseUrlWebPageToHtmlcontent(pkgUrl)
				if err != nil {
					logger.ServiceLogger.Error("Error while parsing Firefox release details :", err.Error())
					break
				}
				fileDataList := service.parsePackageDetailsFromWebPage(htmlContent, pkgUrl, os, languageCode, checkSumMap)
				if fileDataList == nil {
					return
				}
				var osType common.OsType
				var osArch common.OsArchitecture

				osType, osArch = common.ParseOsTypeAndOsArch(os, osType, osArch)

				existingPatch, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(osType), int(common.FIREFOX))
				if existingPatch.Id > 0 {
					DeleteXmlForWindows(existingPatch.Uuid, common.FIREFOX)
					_, _ = thirdPartyRepo.DeletePatch(existingPatch)
				}

				//TODO : handle cve details https://www.mozilla.org/en-US/security/known-vulnerabilities/
				var pkg thirdparty.ThirdPartyPackage
				pkgModel := NewThirdPartyPackageService().Repository.GetByUUId(fireFoxMetaData["uuid"].(string) + "_" + latestVersion)
				if pkgModel.Id > 0 {
					pkg = pkgModel
				} else {
					pkg = thirdparty.ThirdPartyPackage{}
				}
				pkg = thirdparty.ThirdPartyPackage{
					BaseEntityModel: model.BaseEntityModel{
						Name: "Mozilla Firefox",
					},
					Description:      "",
					Version:          latestVersion,
					Os:               osType,
					Arch:             osArch,
					LanguageCode:     languageCode,
					PkgFileData:      []model.FileData{fileDataList[0]},
					LatestPackageUrl: "",
					Publisher:        "Mozilla Foundation & Mozilla Corporation",
					SupportUrl:       "https://www.mozilla.org/" + languageCode,
					ReleaseNote:      "https://www.mozilla.org/" + languageCode + "/firefox/" + latestVersion + "/releasenotes/",
					ReleaseDate:      fileDataList[0].ReleaseDate,
					Application:      common.FIREFOX,
					Uuid:             fireFoxMetaData["uuid"].(string) + "_" + latestVersion,
				}

				if pkgModel.Id > 0 {
					pkg.Id = pkgModel.Id
					pkg.UpdatedTime = time.Now().UnixMilli()
					_, err := NewThirdPartyPackageService().Repository.Update(&pkg)
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
				} else {
					pkg.UpdatedTime = time.Now().UnixMilli()
					pkg.CreatedTime = time.Now().UnixMilli()
					_, err := NewThirdPartyPackageService().Repository.Create(&pkg)
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
				}
				if common.Windows == osType {
					GenerateXmlForWindows(fireFoxMetaData, latestVersion, osArch, pkg.Uuid, common.FIREFOX)
				}
			}
		}
	}
	service.CreateRequiredFiles(common.FIREFOX)
}

func (service FirefoxPatchPoolingService) parseVersionListFromReleasePage(err error, htmlContent string) []string {
	var versionList []string

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		logger.ServiceLogger.Error("Error while Parsing html to doc :", err.Error())
	}

	if doc != nil {
		doc.Find("tr").Each(func(i int, row *goquery.Selection) {
			rowData := make([]string, 0)
			row.Find("td").Each(func(j int, cell *goquery.Selection) {
				rowData = append(rowData, strings.TrimSpace(cell.Text()))
			})
			if len(rowData) > 2 && rowData[1] != "" {
				version := strings.ReplaceAll(rowData[1], "/", "")
				_, err := strconv.ParseFloat(strings.ReplaceAll(version, ".", ""), 64)
				if err == nil {
					versionList = append(versionList, version)
				}
			}
		})
	}
	return versionList
}

func (service FirefoxPatchPoolingService) parseUrlWebPageToHtmlcontent(url string) (string, error) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}
	req, err := http.NewRequest("GET", url, nil)

	if err != nil {
		return "", err
	}

	res, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(res.Body)

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

func (service FirefoxPatchPoolingService) parsePackageDetailsFromWebPage(htmlContent, pkgUrl, os, languageCode string, checkSumMap map[string]string) []model.FileData {
	var fileDataList []model.FileData
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		logger.ServiceLogger.Error("Error while Parsing html to doc :", err.Error())
		return fileDataList
	}

	doc.Find("tr").Each(func(i int, row *goquery.Selection) {
		rowData := make([]string, 0)
		row.Find("td").Each(func(j int, cell *goquery.Selection) {
			rowData = append(rowData, strings.TrimSpace(cell.Text()))
		})
		if len(rowData) == 4 && "file" == strings.ToLower(rowData[0]) && rowData[2] != "" && rowData[3] != "" {
			parsedTime, err := time.Parse("02-Jan-2006 15:04", rowData[3])
			if err != nil {
				logger.ServiceLogger.Error("Error parsing date:", err)
			}
			checkSum := ""
			if val, ok := checkSumMap[strings.Join([]string{os, languageCode, rowData[1]}, "/")]; ok {
				checkSum = val
			}
			fileData := model.FileData{
				FileName:       rowData[1],
				DownloadUrl:    pkgUrl + rowData[1],
				Size:           common.ConvertSizeToBytes(rowData[2]),
				ReleaseDate:    parsedTime.UnixMilli(),
				ChecksumSHA256: checkSum,
			}
			if strings.Contains(rowData[1], ".msi") {
				fileDataList = append(fileDataList, fileData)
			}
		}
	})

	return fileDataList
}
