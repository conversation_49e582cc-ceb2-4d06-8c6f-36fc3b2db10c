package thirdparty

import (
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"io"
	"log"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type AdobePatchPoolingService struct {
	ThirdPartyPackageService
}

func (c AdobePatchPoolingService) Name() string {
	return "AdobePatchPoolingService"
}
func init() {
	RegisterCollector(AdobePatchPoolingService{})
}

var (
	plannedUpdatePattern = regexp.MustCompile(`([0-9]+\.[0-9]+\.[0-9]+[A-Za-z]*) Planned update, ([A-Za-z]+ [0-9]{1,2}, [0-9]{4})`)
	osVersionPattern     = regexp.MustCompile(`([0-9]+\.[0-9]+\.[0-9]+) \(Win\), ([0-9]+\.[0-9]+\.[0-9]+\) \(Mac\) Planned update, ([A-Za-z]+ [0-9]+, [0-9]{4}))`)
	ReleaseNoteUrl       = "https://www.adobe.com/devnet-docs/acrobatetk/tools/ReleaseNotesDC/"
)

var adobeMetaInfo = map[string]interface{}{
	common.ADOBE_ACROBAT.String(): map[string]interface{}{
		"uuid": "6a83b5f7-c478-4a29-b86e-ff7443c0db13",
		"templateFileNameMap": map[string]interface{}{
			"x64": "ADOBE_ACROBAT_X64.xml",
			"x86": "ADOBE_ACROBAT_X32.xml",
		},
		"productCodeMap": map[string]interface{}{
			"x64": "{ac76ba86-1033-ffff-7760-bc15014ea700}",
			"x86": "{ac76ba86-1033-ffff-7760-0c0f074e4100}",
		},
	},
	common.ADOBE_ACROBAT_READER.String(): map[string]interface{}{
		"uuid": "38b0ac75-6d25-4342-a900-0deed3fa55f2",
		"templateFileNameMap": map[string]interface{}{
			"x64": "ADOBE_ACROBAT_READER_X64.xml",
			"x86": "ADOBE_ACROBAT_READER_X32.xml",
		},
		"productCodeMap": map[string]interface{}{
			"x64": "{ac76ba86-1033-1033-7760-bc15014ea700}",
			"x86": "{ac76ba86-7ad7-1033-7b44-ac0f074e4100}",
		},
	},
	common.ADOBE_ACROBAT_READER_MUI.String(): map[string]interface{}{
		"uuid": "c20669d5-e970-4880-99d1-eae632ed116d",
		"templateFileNameMap": map[string]interface{}{
			"x64": "ADOBE_ACROBAT_READER_MUI_X64.xml",
			"x86": "ADOBE_ACROBAT_READER_MUI_X32.xml",
		},
		"productCodeMap": map[string]interface{}{
			"x64": "{AC76BA86-1033-FF00-7760-BC15014EA700}",
			"x86": "{AC76BA86-7AD7-FFFF-7B44-AC0F074E4100}",
		},
	},
}

func NewAdobePatchPoolingService() *AdobePatchPoolingService {
	return &AdobePatchPoolingService{}
}

func (service AdobePatchPoolingService) ExecuteSync() {
	versiondetails := make(map[string]string)

	htmlData := service.fetchData(ReleaseNoteUrl)

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlData))
	if err != nil {
		log.Fatalf("Error parsing document: %v", err)
	}

	divForUpdates := doc.Find("#continuous-track-installers")
	listofRelease := divForUpdates.Find("ul").First()
	elements := listofRelease.Find("li")

	for i := range elements.Nodes {
		element := elements.Eq(i)

		releaseNote := element.Find("p").First().Find("a").First().Find("span").First().Text()

		if matches := plannedUpdatePattern.FindStringSubmatch(releaseNote); matches != nil {
			version := matches[1]
			date := matches[2]
			versiondetails["win"] = version
			versiondetails["mac"] = version
			versiondetails["date"] = date
			service.parseData(element, versiondetails)
			break
		}

		if matches := osVersionPattern.FindStringSubmatch(releaseNote); matches != nil {
			versionWindows := matches[1]
			versionMac := matches[2]
			date := matches[3]
			versiondetails["win"] = versionWindows
			versiondetails["mac"] = versionMac
			versiondetails["date"] = date
			service.parseData(element, versiondetails)
			break
		}
	}

	// Create allpatchlist.txt files for each Adobe application
	for _, application := range []common.ThirdPartyApplication{common.ADOBE_ACROBAT_READER, common.ADOBE_ACROBAT_READER_MUI, common.ADOBE_ACROBAT} {
		// Check if the directory exists before trying to create allpatchlist.txt
		service.CreateRequiredFiles(application)

	}

}

func (service AdobePatchPoolingService) fetchData(url string) string {
	resp, err := http.Get(url)
	if err != nil {
		logger.ServiceLogger.Error("Error fetching data ", err)
		return ""
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		logger.ServiceLogger.Error("Unexpected status code: ", resp.StatusCode)
		return ""
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.ServiceLogger.Error("Error reading response body ", err)
		return ""
	}

	return string(body)
}

func (service AdobePatchPoolingService) parseData(element *goquery.Selection, versiondetails map[string]string) {
	urlTag := element.Find("p a").First()
	releasedetailsUrl, _ := urlTag.Attr("href")

	resp, err := http.Get(ReleaseNoteUrl + releasedetailsUrl)
	if err != nil {
		log.Fatalf("Error fetching release details: %v", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	filedetailsData, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		log.Fatalf("Error reading response body: %v", err)
	}

	releaseFileDetails := filedetailsData.Find("#available-installers")
	releaseTables := releaseFileDetails.Find("table")

	for i := range releaseTables.Nodes {
		releaseTable := releaseTables.Eq(i)

		var osArch string
		osType := common.Windows
		version := versiondetails["win"]
		heading := releaseTable.Find("caption span").Text()

		if strings.Contains(heading, "Windows installers (32-bit)") {
			continue
		} else if strings.Contains(heading, "Windows installers (64-bit)") {
			osArch = "x64"
		} else {
			version = versiondetails["mac"]
			osType = common.MacOS
		}

		releaseElements := releaseTable.Find("tbody tr")

		for _, application := range []common.ThirdPartyApplication{common.ADOBE_ACROBAT_READER, common.ADOBE_ACROBAT_READER_MUI, common.ADOBE_ACROBAT} {

			for j := range releaseElements.Nodes {
				trElements := releaseElements.Eq(j)

				applicationName := trElements.Find("td").Eq(0).Text()
				applicationType := trElements.Find("td").Eq(5).Text()

				switch {
				case strings.EqualFold(applicationName, "Acrobat"):
					if application == common.ADOBE_ACROBAT {
						createReleasePackage(versiondetails, trElements, osArch, version, application, osType)
					}
				case strings.EqualFold(applicationName, "Reader") && strings.Contains(applicationType, "MUI"):
					if application == common.ADOBE_ACROBAT_READER_MUI {
						createReleasePackage(versiondetails, trElements, osArch, version, application, osType)
					}
				case strings.EqualFold(applicationName, "Reader"):
					if application == common.ADOBE_ACROBAT_READER {
						createReleasePackage(versiondetails, trElements, osArch, version, application, osType)
					}
				}
			}
		}
	}
}

func createReleasePackage(versiondetails map[string]string, trElements *goquery.Selection, osArch, version string, application common.ThirdPartyApplication, osType common.OsType) {
	packageDetails := trElements.Eq(0).Text()
	downloadUrl, _ := trElements.Find("td").Eq(2).Find("a").Attr("href")
	fileName := trElements.Find("td").Eq(2).Find("a").Text()
	pkgInfo := strings.Split(packageDetails, "\n")
	if !strings.Contains(strings.ToLower(version), "x") {
		version = extractVersionFromProductName(fileName, version)
	}

	size := float64(0)
	if pkgInfo[3] != "" {
		sizeStr := strings.ToLower(pkgInfo[3])
		sizeStr = strings.ReplaceAll(sizeStr, "mb", "")
		size, _ = strconv.ParseFloat(strings.TrimSpace(sizeStr), 64)
		size *= 1024
	}

	arch := common.X64
	if strings.Contains(osArch, "32") || strings.Contains(osArch, "86") {
		arch = common.X86
	}

	if osType == common.MacOS {
		arch = common.All
	}

	releaseDate := int64(0)
	if date, ok := versiondetails["date"]; ok {
		t, err := time.Parse("Jan 2, 2006", date)
		if err == nil {
			releaseDate = t.UnixMilli()
		} else {
			t, err = time.Parse("January 2, 2006", date)
			if err == nil {
				releaseDate = t.UnixMilli()
			}
		}
	}

	existingPatch, _ := NewThirdPartyPackageService().Repository.GetPkgByPlatformOsArchApplication(int(arch), int(osType), int(application))
	if existingPatch.Id > 0 {
		DeleteXmlForWindows(existingPatch.Uuid, application)
		_, _ = NewThirdPartyPackageService().Repository.DeletePatch(existingPatch)
	}
	uuid := strings.Join([]string{application.String(), version, strings.ToLower(arch.String()), osType.String()}, "-")
	count := NewThirdPartyPackageService().Repository.GetCountByUuid(uuid)
	if count == 0 {
		pkg := thirdparty.ThirdPartyPackage{}
		pkg.Name = pkgInfo[0]
		pkg.PkgFileData = []model.FileData{
			{
				FileName:    fileName,
				DownloadUrl: downloadUrl,
				Size:        int64(size),
				ReleaseDate: releaseDate,
			},
		}
		pkg.Application = application
		pkg.Os = osType
		pkg.Arch = arch
		pkg.Version = version
		pkg.CreatedTime = time.Now().UnixMilli()
		pkg.ReleaseDate = releaseDate
		pkg.LatestPackageUrl = downloadUrl
		pkg.SupportUrl = "https://helpx.adobe.com/enterprise/get-started.html"
		pkg.Publisher = "Adobe"
		pkg.Uuid = uuid
		pkg.UpdatedTime = time.Now().UnixMilli()
		pkg.CreatedTime = time.Now().UnixMilli()
		_, err := NewThirdPartyPackageService().Repository.Create(&pkg)
		if err != nil {
			logger.ServiceLogger.Error("Error while creating adobe third party package for pkg : ", fmt.Sprint(pkg))
		}
		if common.Windows == osType {
			GenerateXmlForWindows(adobeMetaInfo[application.String()].(map[string]interface{}), version, arch, uuid, application)
		}
	}
}

func extractVersionFromProductName(productName string, version string) string {
	plainVersion := strings.ReplaceAll(version, ".", "")
	plainVersion = strings.ReplaceAll(plainVersion, "x", "")

	if strings.Contains(productName, plainVersion) {
		firstIndexOfDot := strings.Index(version, ".")
		version = strings.Replace(version, ".", "", 1)
		secondIndexOfDot := strings.Index(version, ".") + 1
		version = strings.Replace(version, ".", "", 1)

		lastIndex := strings.Index(productName, ".")
		if strings.Contains(productName, "_") {
			lastIndex = strings.Index(productName, "_")
		}

		version = productName[strings.Index(productName, plainVersion):lastIndex]
		version = convertToDottedFormat(version, []int{firstIndexOfDot, secondIndexOfDot})
	}

	return version
}

func convertToDottedFormat(version string, dotIndex []int) string {
	for _, dot := range dotIndex {
		version = version[:dot] + "." + version[dot:]
	}
	return version
}
